const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// اتصال بقاعدة البيانات
async function connectDB() {
  if (mongoose.connection.readyState === 1) {
    return;
  }
  
  const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/law_firm_db';
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to MongoDB');
}

// نموذج المستخدم
const userSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { type: String, enum: ['admin', 'editor', 'user'], default: 'user' },
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const User = mongoose.models.User || mongoose.model('User', userSchema);

async function createAdmin() {
  try {
    await connectDB();
    
    // التحقق من وجود مستخدم admin
    const existingAdmin = await User.findOne({ role: 'admin' });
    if (existingAdmin) {
      console.log('Admin user already exists:', existingAdmin.email);
      process.exit(0);
    }
    
    // إنشاء مستخدم admin جديد
    const hashedPassword = await bcrypt.hash('admin123', 12);
    
    const adminUser = new User({
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      isActive: true
    });
    
    await adminUser.save();
    console.log('Admin user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    
    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

createAdmin();
