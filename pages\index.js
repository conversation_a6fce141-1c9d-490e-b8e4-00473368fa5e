import Head from 'next/head';
import Link from 'next/link';

export default function Home() {

  return (
    <>
      <Head>
        <title>مكتب المحاماة للاستشارات القانونية - الصفحة الرئيسية</title>
        <meta name="description" content="مكتب محاماة متخصص في تقديم الاستشارات القانونية والخدمات القانونية المتكاملة" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div style={{
        minHeight: '100vh',
        fontFamily: 'Arial, sans-serif',
        backgroundColor: '#f8fafc'
      }}>
        {/* Header */}
        <header style={{
          backgroundColor: '#1e40af',
          color: 'white',
          padding: '1rem 0',
          textAlign: 'center'
        }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem' }}>
            <h1 style={{ fontSize: '2rem', margin: '0', fontWeight: 'bold' }}>
              مكتب المحاماة للاستشارات القانونية
            </h1>
            <p style={{ margin: '0.5rem 0 0 0', opacity: 0.9 }}>
              خدمات قانونية متميزة وموثوقة
            </p>
          </div>
        </header>

        {/* Navigation */}
        <nav style={{
          backgroundColor: 'white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          padding: '1rem 0'
        }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem' }}>
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '2rem',
              flexWrap: 'wrap'
            }}>
              <Link href="/" style={{ textDecoration: 'none', color: '#1e40af', fontWeight: '500' }}>
                الرئيسية
              </Link>
              <Link href="/test" style={{ textDecoration: 'none', color: '#1e40af', fontWeight: '500' }}>
                صفحة الاختبار
              </Link>
              <Link href="/api/test-db" style={{ textDecoration: 'none', color: '#1e40af', fontWeight: '500' }}>
                اختبار قاعدة البيانات
              </Link>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main style={{ padding: '3rem 1rem' }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>

            {/* Welcome Section */}
            <section style={{
              backgroundColor: 'white',
              padding: '3rem',
              borderRadius: '10px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              textAlign: 'center',
              marginBottom: '3rem'
            }}>
              <h2 style={{
                fontSize: '2.5rem',
                color: '#1e40af',
                marginBottom: '1rem'
              }}>
                مرحباً بكم في مكتبنا
              </h2>
              <p style={{
                fontSize: '1.2rem',
                color: '#64748b',
                lineHeight: '1.6',
                maxWidth: '800px',
                margin: '0 auto'
              }}>
                نحن مكتب محاماة متخصص يقدم خدمات قانونية شاملة للأفراد والشركات.
                فريقنا من المحامين المتخصصين مستعد لتقديم أفضل الحلول القانونية لكم.
              </p>
            </section>

            {/* Services Grid */}
            <section style={{ marginBottom: '3rem' }}>
              <h3 style={{
                fontSize: '2rem',
                color: '#1e40af',
                textAlign: 'center',
                marginBottom: '2rem'
              }}>
                خدماتنا القانونية
              </h3>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '2rem'
              }}>
                <div style={{
                  backgroundColor: 'white',
                  padding: '2rem',
                  borderRadius: '8px',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                  textAlign: 'center'
                }}>
                  <h4 style={{ color: '#1e40af', marginBottom: '1rem' }}>قانون الشركات</h4>
                  <p style={{ color: '#64748b' }}>تأسيس وإدارة الشركات والمؤسسات التجارية</p>
                </div>

                <div style={{
                  backgroundColor: 'white',
                  padding: '2rem',
                  borderRadius: '8px',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                  textAlign: 'center'
                }}>
                  <h4 style={{ color: '#1e40af', marginBottom: '1rem' }}>التقاضي</h4>
                  <p style={{ color: '#64748b' }}>تمثيل قانوني أمام المحاكم ومراكز التحكيم</p>
                </div>

                <div style={{
                  backgroundColor: 'white',
                  padding: '2rem',
                  borderRadius: '8px',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                  textAlign: 'center'
                }}>
                  <h4 style={{ color: '#1e40af', marginBottom: '1rem' }}>القانون العقاري</h4>
                  <p style={{ color: '#64748b' }}>استشارات ومعاملات عقارية متكاملة</p>
                </div>
              </div>
            </section>

            {/* Contact Section */}
            <section style={{
              backgroundColor: '#1e40af',
              color: 'white',
              padding: '3rem',
              borderRadius: '10px',
              textAlign: 'center'
            }}>
              <h3 style={{ fontSize: '2rem', marginBottom: '1rem' }}>
                تواصل معنا
              </h3>
              <p style={{ fontSize: '1.1rem', marginBottom: '2rem', opacity: 0.9 }}>
                احصل على استشارة قانونية مجانية من خبرائنا
              </p>
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                gap: '1rem',
                flexWrap: 'wrap'
              }}>
                <button style={{
                  backgroundColor: 'white',
                  color: '#1e40af',
                  padding: '0.75rem 1.5rem',
                  border: 'none',
                  borderRadius: '5px',
                  fontWeight: 'bold',
                  cursor: 'pointer'
                }}>
                  اتصل بنا
                </button>
                <button style={{
                  backgroundColor: 'transparent',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  border: '2px solid white',
                  borderRadius: '5px',
                  fontWeight: 'bold',
                  cursor: 'pointer'
                }}>
                  احجز موعد
                </button>
              </div>
            </section>
          </div>
        </main>

        {/* Footer */}
        <footer style={{
          backgroundColor: '#374151',
          color: 'white',
          padding: '2rem 1rem',
          textAlign: 'center'
        }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <p style={{ margin: 0, opacity: 0.8 }}>
              © 2024 مكتب المحاماة للاستشارات القانونية. جميع الحقوق محفوظة.
            </p>
          </div>
        </footer>
      </div>
    </>
  );
}
