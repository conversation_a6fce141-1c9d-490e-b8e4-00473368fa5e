import { connectToMongoDB } from '../../lib/mongodb';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    console.log('🔍 Testing database connection...');
    
    // Test database connection
    const { db } = await connectToMongoDB();
    
    console.log('✅ Database connected successfully');
    
    // Test basic database operations
    const testCollection = db.collection('test');
    
    // Insert a test document
    const testDoc = {
      message: 'Database connection test',
      timestamp: new Date(),
      status: 'success'
    };
    
    const insertResult = await testCollection.insertOne(testDoc);
    console.log('✅ Test document inserted:', insertResult.insertedId);
    
    // Read the test document
    const foundDoc = await testCollection.findOne({ _id: insertResult.insertedId });
    console.log('✅ Test document found:', foundDoc);
    
    // Delete the test document
    await testCollection.deleteOne({ _id: insertResult.insertedId });
    console.log('✅ Test document deleted');
    
    // Get database stats
    const stats = await db.stats();
    
    return res.status(200).json({
      success: true,
      message: 'Database connection successful',
      data: {
        connected: true,
        database: db.databaseName,
        collections: stats.collections || 0,
        dataSize: stats.dataSize || 0,
        indexSize: stats.indexSize || 0,
        testOperations: {
          insert: true,
          read: true,
          delete: true
        }
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Database connection error:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: {
        name: error.name,
        message: error.message,
        code: error.code || 'UNKNOWN_ERROR'
      },
      timestamp: new Date().toISOString()
    });
  }
}
