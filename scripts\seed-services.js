const mongoose = require('mongoose');

// اتصال بقاعدة البيانات
async function connectDB() {
  if (mongoose.connection.readyState === 1) {
    return;
  }

  const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/law_firm_db';
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to MongoDB');
}

// نماذج البيانات
const categorySchema = new mongoose.Schema({
  name: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  type: { type: String, enum: ['article', 'service', 'general'], default: 'general' }
}, { timestamps: true });

const userSchema = new mongoose.Schema({
  username: { type: String, required: true },
  email: { type: String, required: true },
  role: { type: String, enum: ['admin', 'editor', 'user'], default: 'user' }
}, { timestamps: true });

const serviceSchema = new mongoose.Schema({
  title: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  excerpt: { type: String },
  description: { type: String },
  content: { type: String },
  category: { type: mongoose.Schema.Types.ObjectId, ref: 'Category' },
  author: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  tags: [String],
  status: { type: String, enum: ['draft', 'published', 'archived'], default: 'draft' },
  featured: { type: Boolean, default: false },
  readTime: { type: Number, default: 5 },
  views: { type: Number, default: 0 },
  publishDate: { type: Date, default: Date.now },
  requirements: { type: String },
  duration: { type: String },
  price: { type: String }
}, { timestamps: true });

const Category = mongoose.models.Category || mongoose.model('Category', categorySchema);
const User = mongoose.models.User || mongoose.model('User', userSchema);
const Service = mongoose.models.Service || mongoose.model('Service', serviceSchema);

const sampleServices = [
  {
    title: 'تمثيل قانوني في القضايا التجارية',
    slug: 'commercial-legal-representation',
    excerpt: 'نقدم تمثيلاً قانونياً متميزاً في جميع القضايا التجارية أمام المحاكم المختصة',
    description: 'خدمة شاملة للتمثيل القانوني في القضايا التجارية مع فريق من المحامين المتخصصين',
    content: `
      <h2>خدمة التمثيل القانوني في القضايا التجارية</h2>
      
      <p>نقدم في مكتبنا خدمات التمثيل القانوني المتميزة في جميع أنواع القضايا التجارية، حيث يضم فريقنا نخبة من المحامين المتخصصين في القانون التجاري والذين يتمتعون بخبرة واسعة في التعامل مع مختلف أنواع المنازعات التجارية.</p>
      
      <h3>نطاق الخدمة</h3>
      <ul>
        <li>قضايا المنازعات التجارية</li>
        <li>قضايا الشركات والشراكات</li>
        <li>منازعات العقود التجارية</li>
        <li>قضايا الإفلاس والتصفية</li>
        <li>منازعات الملكية الفكرية</li>
        <li>قضايا التأمين التجاري</li>
      </ul>
      
      <h3>مميزات خدمتنا</h3>
      <p>نتميز بالخبرة الواسعة والفهم العميق للقوانين التجارية، مما يمكننا من تقديم أفضل الحلول القانونية لعملائنا.</p>
      
      <h3>عملية العمل</h3>
      <ol>
        <li>دراسة القضية وتحليل الوضع القانوني</li>
        <li>وضع الاستراتيجية القانونية المناسبة</li>
        <li>إعداد المذكرات والمرافعات</li>
        <li>التمثيل أمام المحاكم</li>
        <li>متابعة القضية حتى صدور الحكم النهائي</li>
      </ol>
    `,
    tags: ['تمثيل قانوني', 'قضايا تجارية', 'محاكم', 'مرافعات'],
    status: 'published',
    featured: true,
    readTime: 8,
    views: 245,
    requirements: 'أوراق القضية، العقود ذات الصلة، المراسلات',
    duration: 'حسب طبيعة القضية',
    price: 'يحدد حسب تعقيد القضية'
  },
  {
    title: 'تأسيس الشركات والمؤسسات',
    slug: 'company-establishment',
    excerpt: 'خدمات متكاملة لتأسيس جميع أنواع الشركات والمؤسسات التجارية وفقاً للأنظمة السعودية',
    description: 'نساعدك في تأسيس شركتك من الألف إلى الياء مع ضمان الامتثال لجميع الأنظمة واللوائح',
    content: `
      <h2>خدمة تأسيس الشركات والمؤسسات</h2>
      
      <p>نقدم خدمات شاملة لتأسيس جميع أنواع الشركات والمؤسسات التجارية في المملكة العربية السعودية، مع ضمان الامتثال الكامل لجميع الأنظمة واللوائح المعمول بها.</p>
      
      <h3>أنواع الشركات التي نؤسسها</h3>
      <ul>
        <li>الشركات ذات المسؤولية المحدودة</li>
        <li>شركات المساهمة المقفلة</li>
        <li>شركات التضامن</li>
        <li>الشركات المهنية</li>
        <li>المؤسسات الفردية</li>
        <li>الشركات الأجنبية</li>
      </ul>
      
      <h3>خدماتنا تشمل</h3>
      <ul>
        <li>إعداد عقد التأسيس والنظام الأساسي</li>
        <li>الحصول على السجل التجاري</li>
        <li>التسجيل في الزكاة والضريبة</li>
        <li>فتح الحسابات البنكية</li>
        <li>الحصول على التراخيص المطلوبة</li>
        <li>التسجيل في التأمينات الاجتماعية</li>
      </ul>
      
      <h3>المزايا</h3>
      <p>فريق متخصص، إجراءات سريعة، متابعة شاملة، ضمان الامتثال القانوني.</p>
    `,
    tags: ['تأسيس شركات', 'سجل تجاري', 'تراخيص', 'استثمار'],
    status: 'published',
    featured: true,
    readTime: 6,
    views: 189,
    requirements: 'هوية المؤسسين، عقد الإيجار، رأس المال المطلوب',
    duration: '2-4 أسابيع',
    price: 'من 5,000 ريال'
  },
  {
    title: 'صياغة ومراجعة العقود التجارية',
    slug: 'commercial-contracts-drafting',
    excerpt: 'خدمات احترافية لصياغة ومراجعة جميع أنواع العقود التجارية بأعلى معايير الجودة القانونية',
    description: 'نضمن لك عقود محكمة قانونياً تحمي مصالحك وتقلل من المخاطر القانونية',
    content: `
      <h2>خدمة صياغة ومراجعة العقود التجارية</h2>
      
      <p>نقدم خدمات متخصصة في صياغة ومراجعة جميع أنواع العقود التجارية، حيث نضمن لعملائنا الحصول على عقود محكمة قانونياً تحمي مصالحهم وتقلل من المخاطر القانونية المحتملة.</p>
      
      <h3>أنواع العقود التي نتعامل معها</h3>
      <ul>
        <li>عقود البيع والشراء</li>
        <li>عقود التوريد والتوزيع</li>
        <li>عقود الخدمات</li>
        <li>عقود الشراكة</li>
        <li>عقود العمل والتوظيف</li>
        <li>عقود الإيجار التجاري</li>
        <li>عقود التمويل</li>
        <li>عقود التأمين</li>
      </ul>
      
      <h3>منهجيتنا في العمل</h3>
      <ol>
        <li>فهم احتياجات العميل ومتطلبات العقد</li>
        <li>دراسة الجوانب القانونية والتنظيمية</li>
        <li>صياغة العقد وفقاً لأفضل الممارسات</li>
        <li>المراجعة القانونية الشاملة</li>
        <li>التعديل والتطوير حسب الحاجة</li>
      </ol>
      
      <h3>ضماناتنا</h3>
      <p>نضمن الامتثال الكامل للأنظمة السعودية، وحماية مصالح العميل، وتقليل المخاطر القانونية.</p>
    `,
    tags: ['عقود تجارية', 'صياغة قانونية', 'مراجعة عقود', 'قانون تجاري'],
    status: 'published',
    featured: false,
    readTime: 5,
    views: 156,
    requirements: 'تفاصيل العقد المطلوب، بيانات الأطراف',
    duration: '3-7 أيام عمل',
    price: 'من 2,000 ريال'
  },
  {
    title: 'استشارات قانونية متخصصة',
    slug: 'specialized-legal-consultations',
    excerpt: 'احصل على استشارات قانونية متخصصة من فريق من الخبراء في مختلف فروع القانون',
    description: 'نقدم استشارات قانونية شاملة ومتخصصة لمساعدتك في اتخاذ القرارات الصحيحة',
    content: `
      <h2>خدمة الاستشارات القانونية المتخصصة</h2>
      
      <p>نقدم استشارات قانونية متخصصة وشاملة في جميع فروع القانون، حيث يضم فريقنا خبراء قانونيين متخصصين في مختلف المجالات لضمان تقديم أفضل النصائح والحلول القانونية.</p>
      
      <h3>مجالات الاستشارة</h3>
      <ul>
        <li>القانون التجاري والشركات</li>
        <li>القانون العقاري</li>
        <li>قانون العمل</li>
        <li>القانون الجنائي</li>
        <li>قانون الأسرة</li>
        <li>القانون الإداري</li>
        <li>قانون الملكية الفكرية</li>
        <li>القانون المصرفي</li>
      </ul>
      
      <h3>أنواع الاستشارات</h3>
      <ul>
        <li>الاستشارات الفورية</li>
        <li>الدراسات القانونية المفصلة</li>
        <li>المراجعة القانونية للوثائق</li>
        <li>التدريب القانوني</li>
        <li>إعداد الآراء القانونية</li>
      </ul>
      
      <h3>مميزات خدمتنا</h3>
      <p>خبرة واسعة، استجابة سريعة، حلول عملية، سرية تامة، أسعار تنافسية.</p>
    `,
    tags: ['استشارات قانونية', 'خبراء قانون', 'نصائح قانونية', 'حلول قانونية'],
    status: 'published',
    featured: false,
    readTime: 4,
    views: 98,
    requirements: 'تفاصيل الموضوع، الوثائق ذات الصلة',
    duration: 'فوري إلى 3 أيام',
    price: 'من 500 ريال'
  }
];

async function seedServices() {
  try {
    await connectDB();
    console.log('Connected to MongoDB');

    // البحث عن تصنيف افتراضي
    const defaultCategory = await Category.findOne({ type: 'service' });
    if (!defaultCategory) {
      console.error('No service categories found. Please run seed-categories.js first.');
      process.exit(1);
    }

    // البحث عن مستخدم افتراضي
    const defaultUser = await User.findOne({ role: 'admin' });
    if (!defaultUser) {
      console.error('No admin user found. Please create an admin user first.');
      process.exit(1);
    }

    // حذف الخدمات الموجودة
    await Service.deleteMany({});
    console.log('Deleted existing services');

    // إضافة معرف التصنيف والمؤلف للخدمات
    const servicesWithIds = sampleServices.map(service => ({
      ...service,
      category: defaultCategory._id,
      author: defaultUser._id,
      publishDate: new Date()
    }));

    // إضافة الخدمات الجديدة
    const createdServices = await Service.insertMany(servicesWithIds);
    console.log(`Created ${createdServices.length} services`);

    console.log('Services seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding services:', error);
    process.exit(1);
  }
}

seedServices();
