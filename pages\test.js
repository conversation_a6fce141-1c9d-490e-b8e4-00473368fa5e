import Head from 'next/head';

export default function TestPage() {
  return (
    <>
      <Head>
        <title>صفحة اختبار - Test Page</title>
        <meta name="description" content="صفحة اختبار بسيطة للتحقق من عمل الخادم" />
      </Head>
      
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: 'Arial, sans-serif',
        backgroundColor: '#f0f0f0',
        padding: '20px'
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '10px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          textAlign: 'center',
          maxWidth: '600px'
        }}>
          <h1 style={{
            color: '#2563eb',
            fontSize: '2.5rem',
            marginBottom: '20px'
          }}>
            ✅ صفحة الاختبار تعمل!
          </h1>
          
          <h2 style={{
            color: '#059669',
            fontSize: '2rem',
            marginBottom: '20px'
          }}>
            ✅ Test Page Working!
          </h2>
          
          <div style={{
            backgroundColor: '#f3f4f6',
            padding: '20px',
            borderRadius: '8px',
            marginBottom: '20px'
          }}>
            <h3 style={{ color: '#374151', marginBottom: '10px' }}>معلومات النظام:</h3>
            <p><strong>التاريخ:</strong> {new Date().toLocaleString('ar-SA')}</p>
            <p><strong>الوقت:</strong> {new Date().toLocaleTimeString('ar-SA')}</p>
            <p><strong>المنطقة الزمنية:</strong> {Intl.DateTimeFormat().resolvedOptions().timeZone}</p>
          </div>
          
          <div style={{
            backgroundColor: '#dbeafe',
            padding: '20px',
            borderRadius: '8px',
            marginBottom: '20px'
          }}>
            <h3 style={{ color: '#1e40af', marginBottom: '10px' }}>System Information:</h3>
            <p><strong>Date:</strong> {new Date().toLocaleDateString('en-US')}</p>
            <p><strong>Time:</strong> {new Date().toLocaleTimeString('en-US')}</p>
            <p><strong>User Agent:</strong> {typeof window !== 'undefined' ? window.navigator.userAgent.substring(0, 50) + '...' : 'Server Side'}</p>
          </div>
          
          <div style={{
            display: 'flex',
            gap: '10px',
            justifyContent: 'center',
            flexWrap: 'wrap'
          }}>
            <a 
              href="/"
              style={{
                backgroundColor: '#2563eb',
                color: 'white',
                padding: '10px 20px',
                borderRadius: '5px',
                textDecoration: 'none',
                display: 'inline-block'
              }}
            >
              الصفحة الرئيسية
            </a>
            
            <a 
              href="/ar"
              style={{
                backgroundColor: '#059669',
                color: 'white',
                padding: '10px 20px',
                borderRadius: '5px',
                textDecoration: 'none',
                display: 'inline-block'
              }}
            >
              العربية
            </a>
            
            <a 
              href="/en"
              style={{
                backgroundColor: '#dc2626',
                color: 'white',
                padding: '10px 20px',
                borderRadius: '5px',
                textDecoration: 'none',
                display: 'inline-block'
              }}
            >
              English
            </a>
          </div>
          
          <div style={{
            marginTop: '30px',
            padding: '15px',
            backgroundColor: '#fef3c7',
            borderRadius: '8px',
            border: '1px solid #f59e0b'
          }}>
            <p style={{ color: '#92400e', margin: 0 }}>
              <strong>ملاحظة:</strong> إذا كانت هذه الصفحة تعمل، فإن Next.js والخادم يعملان بشكل صحيح.
            </p>
            <p style={{ color: '#92400e', margin: '5px 0 0 0' }}>
              <strong>Note:</strong> If this page works, Next.js and the server are functioning correctly.
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
