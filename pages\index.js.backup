import Layout from '../components/Layout';
import Hero from '../components/Hero';
import ServiceCard from '../components/ServiceCard';
import TeamMember from '../components/TeamMember';
import QuickLocationCard from '../components/QuickLocationCard';
import Link from 'next/link';
import { 
  FaBuilding, 
  FaGavel, 
  FaHome, 
  FaUsers, 
  FaLightbulb, 
  FaCalculator,
  FaShieldAlt,
  FaHandshake,
  FaAward,
  FaGlobeAmericas,
  FaArrowLeft,
  FaQuoteLeft
} from 'react-icons/fa';

export default function Home() {
  const services = [
    {
      icon: FaBuilding,
      title: 'قانون الشركات والتجارة',
      description: 'خدمات قانونية شاملة لتأسيس وإدارة الشركات والمؤسسات التجارية',
      features: [
        'تأسيس الشركات بجميع أنواعها',
        'صياغة العقود التجارية',
        'الاندماج والاستحواذ',
        'الامتثال التنظيمي'
      ]
    },
    {
      icon: FaGavel,
      title: 'التقاضي وحل النزاعات',
      description: 'تمثيل قانوني متميز أمام جميع المحاكم ومراكز التحكيم',
      features: [
        'التقاضي المدني والتجاري',
        'التحكيم الدولي والمحلي',
        'الوساطة وحل النزاعات',
        'تنفيذ الأحكام والقرارات'
      ]
    },
    {
      icon: FaHome,
      title: 'القانون العقاري',
      description: 'استشارات ومعاملات عقارية متكاملة للأفراد والمطورين',
      features: [
        'شراء وبيع العقارات',
        'تطوير المشاريع العقارية',
        'عقود الإيجار والتمليك',
        'تسوية المنازعات العقارية'
      ]
    },
    {
      icon: FaUsers,
      title: 'قانون العمل والتوظيف',
      description: 'حلول قانونية شاملة لعلاقات العمل وحقوق الموظفين',
      features: [
        'عقود العمل والتوظيف',
        'سياسات الموارد البشرية',
        'منازعات العمل',
        'التأمينات الاجتماعية'
      ]
    },
    {
      icon: FaLightbulb,
      title: 'الملكية الفكرية',
      description: 'حماية وإدارة حقوق الملكية الفكرية والعلامات التجارية',
      features: [
        'تسجيل العلامات التجارية',
        'حماية حقوق الطبع والنشر',
        'براءات الاختراع',
        'مكافحة التقليد والقرصنة'
      ]
    },
    {
      icon: FaCalculator,
      title: 'الاستشارات الضريبية',
      description: 'استشارات ضريبية متخصصة وتخطيط ضريبي فعال',
      features: [
        'التخطيط الضريبي',
        'الامتثال الضريبي',
        'المنازعات الضريبية',
        'الاستشارات الجمركية'
      ]
    }
  ];

  const whyChooseUs = [
    {
      icon: FaShieldAlt,
      title: 'خبرة وثقة',
      description: 'أكثر من 15 عاماً من الخبرة في تقديم الخدمات القانونية المتميزة'
    },
    {
      icon: FaHandshake,
      title: 'حلول مخصصة',
      description: 'نقدم حلولاً قانونية مصممة خصيصاً لتلبية احتياجات كل عميل'
    },
    {
      icon: FaAward,
      title: 'التزام بالنتائج',
      description: 'نلتزم بتحقيق أفضل النتائج الممكنة لعملائنا في جميع القضايا'
    },
    {
      icon: FaGlobeAmericas,
      title: 'فهم عميق للسوق',
      description: 'فهم شامل للقوانين المحلية والدولية والممارسات التجارية'
    }
  ];

  const teamMembers = [
    {
      name: 'د. أحمد محمد العلي',
      position: 'الشريك المؤسس والمدير التنفيذي',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      bio: 'خبرة تزيد عن 20 عاماً في القانون التجاري والشركات، حاصل على دكتوراه في القانون من جامعة الملك سعود',
      specializations: ['قانون الشركات', 'التحكيم التجاري', 'الاندماج والاستحواذ'],
      email: '<EMAIL>',
      phone: '+966 11 123 4567'
    },
    {
      name: 'أ. فاطمة سالم النحاس',
      position: 'شريك أول - قانون العمل',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      bio: 'متخصصة في قانون العمل والتوظيف مع خبرة 15 عاماً، حاصلة على ماجستير في قانون العمل',
      specializations: ['قانون العمل', 'التأمينات الاجتماعية', 'منازعات العمل'],
      email: '<EMAIL>',
      phone: '+966 11 123 4568'
    },
    {
      name: 'أ. خالد عبدالله الزهراني',
      position: 'شريك - القانون العقاري',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      bio: 'خبير في القانون العقاري والتطوير العقاري مع خبرة 12 عاماً في السوق السعودي',
      specializations: ['القانون العقاري', 'تطوير المشاريع', 'التمويل العقاري'],
      email: '<EMAIL>',
      phone: '+966 11 123 4569'
    }
  ];

  const testimonials = [
    {
      name: 'م. سارة أحمد',
      company: 'شركة التقنية المتقدمة',
      text: 'تعاملنا مع المكتب في عدة قضايا قانونية معقدة، وكانت النتائج مذهلة. فريق محترف ومتفهم لاحتياجات العمل.',
      rating: 5
    },
    {
      name: 'أ. محمد الغامدي',
      company: 'مجموعة الغامدي التجارية',
      text: 'خدمة متميزة في مجال قانون الشركات. ساعدونا في تأسيس شركتنا وتنظيم جميع الأمور القانونية بكفاءة عالية.',
      rating: 5
    }
  ];

  return (
    <Layout 
      title="مكتب المحاماة للاستشارات القانونية - الصفحة الرئيسية"
      description="مكتب محاماة متخصص في تقديم الاستشارات القانونية والخدمات القانونية المتكاملة للأفراد والشركات بأعلى معايير الجودة والاحترافية"
    >
      {/* Hero Section */}
      <Hero />

      {/* About Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-6">
                مكتب المحاماة للاستشارات القانونية
                <span className="block text-gold-600 text-2xl mt-2">رواد الاستشارات القانونية</span>
              </h2>
              <p className="text-gray-700 leading-relaxed mb-6">
                نحن مكتب محاماة متخصص يضم نخبة من أفضل المحامين والمستشارين القانونيين في المملكة العربية السعودية. 
                نقدم خدمات قانونية متكاملة للأفراد والشركات مع التركيز على تحقيق أفضل النتائج لعملائنا.
              </p>
              <p className="text-gray-700 leading-relaxed mb-8">
                منذ تأسيسنا، نلتزم بأعلى معايير النزاهة والاحترافية والتميز في تقديم الخدمات القانونية. 
                فريقنا المتخصص يجمع بين الخبرة العملية العميقة والمعرفة الأكاديمية المتقدمة.
              </p>
              <Link href="/about">
                <button className="btn-primary flex items-center">
                  اعرف المزيد عنا
                  <FaArrowLeft className="mr-2" />
                </button>
              </Link>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="مكتب المحاماة"
                className="rounded-lg shadow-xl"
              />
              <div className="absolute -bottom-6 -right-6 bg-white p-6 rounded-lg shadow-lg">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-900">15+</div>
                  <div className="text-sm text-gray-600">سنة خبرة</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-4">
              مجالات خبرتنا القانونية
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              نقدم خدمات قانونية شاملة ومتخصصة تغطي جميع جوانب القانون لتلبية احتياجات عملائنا المتنوعة
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <ServiceCard key={index} {...service} />
            ))}
          </div>
          <div className="text-center mt-12">
            <Link href="/services">
              <button className="btn-secondary">
                عرض جميع الخدمات
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Electronic Services Section */}
      <section className="py-16 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-4">
              الخدمات الإلكترونية المتطورة
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              استفد من خدماتنا القانونية المتطورة عبر المنصات الإلكترونية الآمنة والمعتمدة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <div className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3 text-center">التوقيع الإلكتروني</h3>
              <p className="text-gray-600 text-center">
                وقع على العقود والوثائق القانونية بطريقة آمنة ومعتمدة قانونياً
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3 text-center">استشارة عبر الفيديو</h3>
              <p className="text-gray-600 text-center">
                احصل على استشارة قانونية متخصصة من خبرائنا عبر مكالمة فيديو مباشرة
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3 text-center">صياغة العقود الإلكترونية</h3>
              <p className="text-gray-600 text-center">
                احصل على عقود واتفاقيات قانونية مصاغة بطريقة احترافية ومتوافقة مع القوانين
              </p>
            </div>
          </div>

          <div className="text-center">
            <Link href="/electronic-services">
              <button className="bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors duration-200 shadow-lg">
                استكشف جميع الخدمات الإلكترونية
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-primary-950 text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              لماذا يثق بنا عملاؤنا؟
            </h2>
            <p className="text-gray-300 max-w-2xl mx-auto">
              نتميز بمجموعة من الخصائص التي تجعلنا الخيار الأول للعملاء الباحثين عن خدمات قانونية متميزة
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {whyChooseUs.map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gold-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <item.icon className="text-white text-2xl" />
                </div>
                <h3 className="text-xl font-bold mb-3">{item.title}</h3>
                <p className="text-gray-300">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-4">
              تعرف على خبرائنا القانونيين
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              فريق من أفضل المحامين والمستشارين القانونيين ذوي الخبرة والكفاءة العالية
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <TeamMember key={index} {...member} />
            ))}
          </div>
          <div className="text-center mt-12">
            <Link href="/team">
              <button className="btn-primary">
                تعرف على الفريق كاملاً
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-4">
              ماذا يقول عملاؤنا عنا؟
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              شهادات حقيقية من عملائنا تعكس جودة خدماتنا ومستوى رضاهم
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                <FaQuoteLeft className="text-gold-500 text-3xl mb-4" />
                <p className="text-gray-700 leading-relaxed mb-6">
                  "{testimonial.text}"
                </p>
                <div className="flex items-center">
                  <div>
                    <h4 className="font-bold text-primary-900">{testimonial.name}</h4>
                    <p className="text-gray-600 text-sm">{testimonial.company}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Location Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[#0A2A4E] mb-4">
              موقع مكتبنا
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              نحن في خدمتكم في موقع متميز بالقاهرة. زوروا مكتبنا أو تواصلوا معنا للحصول على استشارة قانونية متخصصة
            </p>
          </div>

          <div className="max-w-md mx-auto">
            <QuickLocationCard />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-primary-900 to-primary-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            هل تحتاج إلى استشارة قانونية؟
          </h2>
          <p className="text-xl mb-8 text-gray-200">
            تواصل معنا اليوم واحصل على استشارة مجانية من خبرائنا القانونيين
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <button className="btn-secondary">
                اتصل بنا الآن
              </button>
            </Link>
            <button className="bg-white text-primary-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200">
              احجز استشارة مجانية
            </button>
          </div>
        </div>
      </section>
    </Layout>
  );
}
