const mongoose = require('mongoose');

// اتصال بقاعدة البيانات
async function connectDB() {
  if (mongoose.connection.readyState === 1) {
    return;
  }

  const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/law_firm_db';
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to MongoDB');
}

// نموذج التصنيف
const categorySchema = new mongoose.Schema({
  name: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  description: { type: String },
  type: { type: String, enum: ['article', 'service', 'general'], default: 'general' },
  color: { type: String, default: '#3B82F6' },
  order: { type: Number, default: 0 },
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const Category = mongoose.models.Category || mongoose.model('Category', categorySchema);

const serviceCategories = [
  {
    name: 'المنازعات القضائية',
    slug: 'judicial-disputes',
    description: 'تمثيل قانوني متميز أمام جميع المحاكم ومراكز التحكيم',
    type: 'service',
    color: '#3B82F6',
    order: 1
  },
  {
    name: 'الشركات والاستثمار',
    slug: 'corporate-investment',
    description: 'خدمات قانونية شاملة لتأسيس وإدارة الشركات والاستثمارات',
    type: 'service',
    color: '#10B981',
    order: 2
  },
  {
    name: 'التحكيم وتسوية المنازعات',
    slug: 'arbitration-disputes',
    description: 'حلول بديلة لفض النزاعات خارج المحاكم',
    type: 'service',
    color: '#8B5CF6',
    order: 3
  },
  {
    name: 'الاستشارات القانونية',
    slug: 'legal-advice',
    description: 'استشارات قانونية متخصصة في جميع فروع القانون',
    type: 'service',
    color: '#F59E0B',
    order: 4
  },
  {
    name: 'القطاع العقاري',
    slug: 'property-sector',
    description: 'استشارات ومعاملات عقارية متكاملة',
    type: 'service',
    color: '#EF4444',
    order: 5
  },
  {
    name: 'شؤون الأجانب والمستثمرين',
    slug: 'foreigners-investors',
    description: 'خدمات قانونية متخصصة للمستثمرين الأجانب',
    type: 'service',
    color: '#6366F1',
    order: 6
  },
  {
    name: 'اللوائح الحكومية والتراخيص',
    slug: 'government-regulations',
    description: 'إدارة العلاقات الحكومية والحصول على التراخيص',
    type: 'service',
    color: '#14B8A6',
    order: 7
  },
  {
    name: 'قطاع العقود',
    slug: 'contracts-sector',
    description: 'صياغة ومراجعة العقود بجميع أنواعها',
    type: 'service',
    color: '#F97316',
    order: 8
  }
];

async function seedCategories() {
  try {
    await connectDB();
    console.log('Connected to MongoDB');

    // حذف التصنيفات الموجودة للخدمات
    await Category.deleteMany({ type: 'service' });
    console.log('Deleted existing service categories');

    // إضافة التصنيفات الجديدة
    const createdCategories = await Category.insertMany(serviceCategories);
    console.log(`Created ${createdCategories.length} service categories`);

    console.log('Categories seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding categories:', error);
    process.exit(1);
  }
}

seedCategories();
