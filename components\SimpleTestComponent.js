import React, { useState } from 'react';

function SimpleTestComponent() {
  const [count, setCount] = useState(0);
  const [message, setMessage] = useState('مرحباً! هذا اختبار Fast Refresh');

  const handleIncrement = () => {
    setCount(prev => prev + 1);
  };

  const handleReset = () => {
    setCount(0);
  };

  const handleMessageChange = () => {
    const messages = [
      'مرحباً! هذا اختبار Fast Refresh',
      'Fast Refresh يعمل بشكل ممتاز! 🚀',
      'التغييرات تظهر فوراً! ✨',
      'Next.js رائع للتطوير! 💯'
    ];
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    setMessage(randomMessage);
  };

  return (
    <div className="p-6 border-2 border-blue-300 rounded-lg bg-blue-50 shadow-lg">
      <h2 className="text-2xl font-bold mb-4 text-blue-800">🧪 اختبار Fast Refresh البسيط</h2>
      
      <div className="mb-4 p-3 bg-white rounded border">
        <p className="text-lg mb-2"><strong>العداد:</strong> {count}</p>
        <p className="text-base mb-2 text-gray-700">{message}</p>
      </div>
      
      <div className="space-x-2 space-x-reverse mb-4">
        <button 
          onClick={handleIncrement}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
        >
          زيادة العداد (+1)
        </button>
        
        <button 
          onClick={handleReset}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
        >
          إعادة تعيين
        </button>
        
        <button 
          onClick={handleMessageChange}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors"
        >
          تغيير الرسالة
        </button>
      </div>
      
      <div className="p-3 bg-yellow-100 rounded border-l-4 border-yellow-500">
        <p className="text-sm text-yellow-800">
          <strong>تعليمات الاختبار:</strong> قم بتعديل هذا المكون وحفظ الملف. يجب أن ترى التغييرات فوراً دون إعادة تحميل الصفحة.
        </p>
      </div>
    </div>
  );
}

export default SimpleTestComponent;
