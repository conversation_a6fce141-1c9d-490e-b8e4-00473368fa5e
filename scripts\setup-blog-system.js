#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 إعداد نظام المدونة للخدمات القانونية...\n');

// 1. تنظيف الكاش
console.log('1️⃣ تنظيف ذاكرة التخزين المؤقت...');
try {
  // حذف مجلد .next
  if (fs.existsSync('.next')) {
    fs.rmSync('.next', { recursive: true, force: true });
    console.log('   ✅ تم حذف مجلد .next');
  }
  
  // حذف مجلد node_modules/.cache
  if (fs.existsSync('node_modules/.cache')) {
    fs.rmSync('node_modules/.cache', { recursive: true, force: true });
    console.log('   ✅ تم حذف مجلد node_modules/.cache');
  }
  
  console.log('   ✅ تم تنظيف ذاكرة التخزين المؤقت\n');
} catch (error) {
  console.log('   ⚠️ خطأ في تنظيف الكاش:', error.message);
}

// 2. إعادة تثبيت التبعيات
console.log('2️⃣ إعادة تثبيت التبعيات...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('   ✅ تم إعادة تثبيت التبعيات\n');
} catch (error) {
  console.log('   ❌ فشل في إعادة تثبيت التبعيات:', error.message);
  process.exit(1);
}

// 3. إنشاء التصنيفات
console.log('3️⃣ إنشاء تصنيفات الخدمات...');
try {
  execSync('node scripts/seed-categories.js', { stdio: 'inherit' });
  console.log('   ✅ تم إنشاء التصنيفات\n');
} catch (error) {
  console.log('   ⚠️ خطأ في إنشاء التصنيفات:', error.message);
}

// 4. إنشاء الخدمات التجريبية
console.log('4️⃣ إنشاء خدمات تجريبية...');
try {
  execSync('node scripts/seed-services.js', { stdio: 'inherit' });
  console.log('   ✅ تم إنشاء الخدمات التجريبية\n');
} catch (error) {
  console.log('   ⚠️ خطأ في إنشاء الخدمات:', error.message);
}

// 5. بناء المشروع
console.log('5️⃣ بناء المشروع...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('   ✅ تم بناء المشروع بنجاح\n');
} catch (error) {
  console.log('   ❌ فشل في بناء المشروع:', error.message);
  console.log('   ℹ️ سيتم تشغيل المشروع في وضع التطوير\n');
}

// 6. تشغيل المشروع
console.log('6️⃣ تشغيل المشروع...');
console.log('🎉 تم إعداد نظام المدونة بنجاح!');
console.log('\n📋 الخطوات التالية:');
console.log('   1. تشغيل المشروع: npm run dev');
console.log('   2. زيارة الصفحة: http://localhost:3000/ar/services');
console.log('   3. تسجيل الدخول للإدارة: http://localhost:3000/admin/login');
console.log('\n🔑 بيانات تسجيل الدخول:');
console.log('   البريد الإلكتروني: <EMAIL>');
console.log('   كلمة المرور: admin123');
console.log('\n✨ ميزات النظام الجديد:');
console.log('   ✅ واجهة مدونة احترافية للخدمات');
console.log('   ✅ نظام بحث وفلترة متقدم');
console.log('   ✅ إدارة محتوى متطورة');
console.log('   ✅ نظام وسوم وخدمات ذات صلة');
console.log('   ✅ إحصائيات ومشاهدات');
console.log('   ✅ مشاركة على وسائل التواصل');
console.log('\n🚀 المشروع جاهز للاستخدام!');
