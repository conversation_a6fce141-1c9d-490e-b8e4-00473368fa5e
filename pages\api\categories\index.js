import connectDB from '../../../lib/mongodb';
import Category from '../../../lib/models/Category';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    await connectDB();

    const { type } = req.query;

    let query = {};
    if (type) {
      query.type = type;
    }

    const categories = await Category.find(query)
      .sort({ name: 1 })
      .select('-__v');

    return res.status(200).json({
      success: true,
      categories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب التصنيفات'
    });
  }
}
