import mongoose from 'mongoose';
import { MongoClient } from 'mongodb';

// إعدادات الاتصال بـ MongoDB
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/law_firm_db';

// خيارات الاتصال
const options = {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  family: 4
};

// متغير لتتبع حالة الاتصال
let isConnected = false;

/**
 * الاتصال بقاعدة بيانات MongoDB
 */
export async function connectToDatabase() {
  if (isConnected) {
    console.log('Already connected to MongoDB');
    return mongoose.connection;
  }

  try {
    console.log('Connecting to MongoDB...');

    // الاتصال بقاعدة البيانات
    await mongoose.connect(MONGODB_URI, options);

    isConnected = true;
    console.log('Successfully connected to MongoDB');

    // معالجة أحداث الاتصال
    mongoose.connection.on('error', (error) => {
      console.error('MongoDB connection error:', error);
      isConnected = false;
    });

    mongoose.connection.on('disconnected', () => {
      console.log('MongoDB disconnected');
      isConnected = false;
    });

    mongoose.connection.on('reconnected', () => {
      console.log('MongoDB reconnected');
      isConnected = true;
    });

    return mongoose.connection;
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    isConnected = false;
    throw error;
  }
}

/**
 * قطع الاتصال من قاعدة البيانات
 */
export async function disconnectFromDatabase() {
  if (!isConnected) {
    return;
  }

  try {
    await mongoose.disconnect();
    isConnected = false;
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error disconnecting from MongoDB:', error);
    throw error;
  }
}

/**
 * التحقق من حالة الاتصال
 */
export function isMongoConnected() {
  return isConnected && mongoose.connection.readyState === 1;
}

/**
 * الحصول على معلومات قاعدة البيانات
 */
export async function getDatabaseInfo() {
  if (!isMongoConnected()) {
    throw new Error('Not connected to MongoDB');
  }

  try {
    const admin = mongoose.connection.db.admin();
    const info = await admin.serverStatus();

    return {
      host: info.host,
      version: info.version,
      uptime: info.uptime,
      connections: info.connections,
      memory: info.mem,
      network: info.network
    };
  } catch (error) {
    console.error('Error getting database info:', error);
    throw error;
  }
}

/**
 * إنشاء الفهارس المطلوبة
 */
export async function createIndexes() {
  if (!isMongoConnected()) {
    await connectToDatabase();
  }

  try {
    // الفهارس يتم إنشاؤها تلقائياً من النماذج (Models)
    // لا حاجة لإنشاء فهارس مكررة هنا
    console.log('Database connection established. Indexes are managed by Mongoose models.');
  } catch (error) {
    console.error('Error in database setup:', error);
    throw error;
  }
}

/**
 * Native MongoDB connection for direct database operations
 */
let client;
let db;

export async function connectToMongoDB() {
  try {
    if (!client) {
      client = new MongoClient(MONGODB_URI, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      });

      await client.connect();
      console.log('✅ Connected to MongoDB (Native Driver)');
    }

    if (!db) {
      db = client.db();
    }

    return { client, db };
  } catch (error) {
    console.error('❌ MongoDB Native connection error:', error);
    throw error;
  }
}

// تصدير الاتصال الافتراضي
export default mongoose;
