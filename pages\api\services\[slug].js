import { connectToDatabase } from '../../../lib/mongodb';
import Service from '../../../lib/models/Service';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  const { slug } = req.query;

  if (!slug) {
    return res.status(400).json({
      success: false,
      message: 'معرف الخدمة مطلوب'
    });
  }

  try {
    await connectToDatabase();

    // جلب الخدمة مع زيادة عدد المشاهدات
    const service = await Service.findOne({
      slug,
      status: 'published'
    })
    .populate('category', 'name slug')
    .populate('author', 'username email')
    .populate('relatedServices', 'title slug excerpt coverImage image views readTime category')
    .select('-__v');

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'الخدمة غير موجودة أو غير منشورة'
      });
    }

    // زيادة عدد المشاهدات
    await service.incrementViews();

    // جلب الخدمات ذات الصلة إذا لم تكن محددة مسبقاً
    let relatedServices = [];
    if (service.relatedServices && service.relatedServices.length > 0) {
      relatedServices = service.relatedServices;
    } else {
      relatedServices = await Service.getRelatedServices(
        service._id,
        service.category._id || service.category,
        4
      );
    }

    return res.status(200).json({
      success: true,
      service,
      relatedServices
    });
  } catch (error) {
    console.error('Error fetching service:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الخدمة'
    });
  }
}
