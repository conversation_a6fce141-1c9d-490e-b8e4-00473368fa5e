import { NextResponse } from 'next/server';
import { locales, defaultLocale, isValidLocale } from './lib/i18n';

export function middleware(request) {
  const { pathname } = request.nextUrl;

  // حماية مسارات الإدارة
  if (pathname.startsWith('/admin')) {
    // السماح بصفحة تسجيل الدخول
    if (pathname === '/admin/login') {
      return NextResponse.next();
    }

    // التحقق من وجود token في الكوكيز
    const token = request.cookies.get('token');

    if (!token) {
      // إعادة توجيه إلى صفحة تسجيل الدخول
      const loginUrl = new URL('/admin/login', request.url);
      return NextResponse.redirect(loginUrl);
    }
  }

  // التعامل مع تعدد اللغات للصفحات العامة
  if (!pathname.startsWith('/admin') && !pathname.startsWith('/api') && !pathname.startsWith('/_next')) {
    // التحقق من وجود لغة في المسار
    const segments = pathname.split('/');
    const potentialLocale = segments[1];

    // إذا لم تكن هناك لغة في المسار، إعادة توجيه للغة الافتراضية
    if (!isValidLocale(potentialLocale)) {
      const url = new URL(`/${defaultLocale}${pathname}`, request.url);
      return NextResponse.redirect(url);
    }

    // إذا كانت اللغة صحيحة، المتابعة
    if (isValidLocale(potentialLocale)) {
      return NextResponse.next();
    }
  }

  // إضافة headers أمان إضافية
  const response = NextResponse.next();

  // منع تضمين الموقع في iframe
  response.headers.set('X-Frame-Options', 'DENY');

  // منع MIME type sniffing
  response.headers.set('X-Content-Type-Options', 'nosniff');

  // تحسين الأمان للمتصفحات القديمة
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // إعدادات Referrer Policy
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin');

  // إضافة CSP headers للأمان
  const cspHeader = `
    default-src 'self';
    script-src 'self' 'unsafe-eval' 'unsafe-inline';
    style-src 'self' 'unsafe-inline';
    img-src 'self' blob: data: https:;
    font-src 'self';
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    upgrade-insecure-requests;
  `.replace(/\s{2,}/g, ' ').trim();

  response.headers.set('Content-Security-Policy', cspHeader);

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - uploads (uploaded files)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|uploads).*)',
  ],
};
