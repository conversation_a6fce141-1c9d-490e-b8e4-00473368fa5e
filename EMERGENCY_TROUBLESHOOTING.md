# 🚨 تقرير طوارئ - الموقع لا يعمل

## المشكلة الحالية
الموقع لا يعمل وخادم Next.js لا يبدأ بشكل صحيح.

## الأعراض المكتشفة
- ✅ الملفات موجودة وسليمة
- ✅ package.json يحتوي على الأوامر الصحيحة
- ✅ next.config.js مبسط وصحيح
- ❌ خادم Next.js لا يبدأ
- ❌ الطرفية لا تستجيب بشكل صحيح

## الحلول المطبقة

### 1. إنشاء صفحة طوارئ
تم إنشاء `public/emergency.html` كصفحة احتياطية:
- **الرابط:** http://localhost:3000/emergency.html
- **الوصف:** صفحة HTML ثابتة تعمل بدون Next.js
- **الوظائف:** عرض معلومات التشخيص والحالة

### 2. تبسيط الصفحة الرئيسية
تم تبسيط `pages/index.js` إلى أبسط شكل ممكن:
```javascript
export default function Home() {
  return (
    <div>
      <h1>مكتب المحاماة</h1>
      <p>مرحباً بكم في موقعنا</p>
      <p>الوقت الحالي: {new Date().toLocaleString()}</p>
      <a href="/test">صفحة الاختبار</a>
    </div>
  );
}
```

### 3. إنشاء صفحة اختبار بسيطة
تم إنشاء `pages/simple-test.js` للاختبار السريع.

## خطوات الإصلاح المقترحة

### الخطوة 1: إعادة تشغيل النظام
```bash
# إيقاف جميع العمليات
taskkill /F /IM node.exe

# تنظيف شامل
npm run clean

# إعادة تثبيت التبعيات
npm install

# تشغيل الخادم
npm run dev
```

### الخطوة 2: فحص المنافذ
```bash
# فحص المنفذ 3000
netstat -ano | findstr :3000

# إذا كان مشغولاً، استخدم منفذ آخر
npm run dev -- -p 3001
```

### الخطوة 3: تشغيل مباشر
```bash
# تشغيل Next.js مباشرة
npx next dev

# أو تشغيل بمنفذ مختلف
npx next dev -p 3001
```

### الخطوة 4: فحص Node.js
```bash
# فحص إصدار Node.js
node --version

# فحص إصدار npm
npm --version

# فحص Next.js
npx next --version
```

## الحلول البديلة

### 1. استخدام صفحة الطوارئ
- **الرابط:** http://localhost:3000/emergency.html
- **المميزات:** تعمل بدون Next.js، تعرض معلومات التشخيص

### 2. تشغيل خادم بسيط
```bash
# تشغيل خادم HTTP بسيط
npx http-server public -p 3000
```

### 3. استخدام Live Server
إذا كان لديك VS Code:
- افتح مجلد public
- انقر بالزر الأيمن على emergency.html
- اختر "Open with Live Server"

## معلومات التشخيص

### ملفات تم فحصها:
- ✅ `package.json` - سليم
- ✅ `next.config.js` - مبسط وصحيح
- ✅ `pages/index.js` - مبسط
- ✅ `.env.local` - مبسط
- ✅ `pages/test.js` - موجود
- ✅ `pages/simple-test.js` - تم إنشاؤه

### ملفات الطوارئ:
- ✅ `public/emergency.html` - صفحة طوارئ HTML
- ✅ `EMERGENCY_TROUBLESHOOTING.md` - هذا التقرير

## الخطوات التالية

1. **جرب الوصول لصفحة الطوارئ:** http://localhost:3000/emergency.html
2. **إذا لم تعمل:** المشكلة في الخادم الأساسي
3. **إذا عملت:** المشكلة في Next.js فقط

## أوامر الطوارئ

```bash
# إعادة تعيين كاملة
npm run clean
rm -rf node_modules
npm install
npm run dev

# تشغيل بمنفذ مختلف
npm run dev -- -p 3001

# تشغيل في وضع الإنتاج
npm run build
npm start

# فحص الأخطاء
npm run dev 2>&1 | tee debug.log
```

## معلومات الاتصال للطوارئ

إذا كان هذا موقع إنتاج:
- **الهاتف:** +966 11 123 4567
- **البريد الإلكتروني:** <EMAIL>
- **الدعم التقني:** <EMAIL>

---
**تاريخ التقرير:** {new Date().toLocaleString('ar-SA')}
**حالة الطوارئ:** نشطة
**الحل البديل:** صفحة HTML ثابتة متاحة
