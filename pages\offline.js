import Head from 'next/head';
import Link from 'next/link';
import { FaWifi, FaHome, FaRedo } from 'react-icons/fa';

export default function OfflinePage() {
  const handleRetry = () => {
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  return (
    <>
      <Head>
        <title>غير متصل بالإنترنت - مكتب المحاماة</title>
        <meta name="description" content="يبدو أنك غير متصل بالإنترنت. تحقق من اتصالك وحاول مرة أخرى." />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center px-4">
        <div className="max-w-md w-full text-center">
          {/* Icon */}
          <div className="mb-8">
            <div className="w-24 h-24 mx-auto bg-gray-200 rounded-full flex items-center justify-center">
              <FaWifi className="text-4xl text-gray-400" />
            </div>
          </div>

          {/* Title */}
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            غير متصل بالإنترنت
          </h1>

          {/* Description */}
          <p className="text-gray-600 mb-8 leading-relaxed">
            يبدو أنك غير متصل بالإنترنت حالياً. تحقق من اتصالك بالشبكة وحاول مرة أخرى.
          </p>

          {/* Offline Features */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              ما يمكنك فعله الآن:
            </h2>
            <ul className="text-right space-y-3 text-gray-600">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-primary-600 rounded-full ml-3"></span>
                تحقق من اتصالك بالإنترنت
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-primary-600 rounded-full ml-3"></span>
                تأكد من تشغيل الواي فاي أو البيانات
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-primary-600 rounded-full ml-3"></span>
                حاول إعادة تحميل الصفحة
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-primary-600 rounded-full ml-3"></span>
                تصفح الصفحات المحفوظة مؤقتاً
              </li>
            </ul>
          </div>

          {/* Actions */}
          <div className="space-y-4">
            <button
              onClick={handleRetry}
              className="w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 transition-colors duration-200 flex items-center justify-center font-medium"
            >
              <FaRedo className="ml-2" />
              إعادة المحاولة
            </button>

            <Link href="/">
              <button className="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center font-medium">
                <FaHome className="ml-2" />
                العودة للصفحة الرئيسية
              </button>
            </Link>
          </div>

          {/* Tips */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-sm font-semibold text-blue-900 mb-2">💡 نصيحة</h3>
            <p className="text-sm text-blue-800">
              يمكنك إضافة هذا الموقع إلى الشاشة الرئيسية لتصفح أسرع وإمكانية الوصول حتى بدون إنترنت.
            </p>
          </div>

          {/* Network Status */}
          <div className="mt-6 text-xs text-gray-500">
            <div id="network-status" className="flex items-center justify-center">
              <span className="w-2 h-2 bg-red-500 rounded-full ml-2 animate-pulse"></span>
              غير متصل
            </div>
          </div>
        </div>
      </div>

      {/* Network Detection Script */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            function updateNetworkStatus() {
              const statusElement = document.getElementById('network-status');
              if (navigator.onLine) {
                statusElement.innerHTML = '<span class="w-2 h-2 bg-green-500 rounded-full ml-2"></span>متصل';
                // إعادة تحميل الصفحة عند عودة الاتصال
                setTimeout(() => {
                  window.location.reload();
                }, 1000);
              } else {
                statusElement.innerHTML = '<span class="w-2 h-2 bg-red-500 rounded-full ml-2 animate-pulse"></span>غير متصل';
              }
            }

            // مراقبة حالة الشبكة
            window.addEventListener('online', updateNetworkStatus);
            window.addEventListener('offline', updateNetworkStatus);
            
            // تحديث الحالة عند تحميل الصفحة
            updateNetworkStatus();

            // فحص دوري للاتصال
            setInterval(() => {
              fetch('/api/health', { 
                method: 'HEAD',
                cache: 'no-cache'
              })
              .then(() => {
                if (!navigator.onLine) {
                  // تحديث حالة المتصفح
                  window.dispatchEvent(new Event('online'));
                }
              })
              .catch(() => {
                if (navigator.onLine) {
                  // تحديث حالة المتصفح
                  window.dispatchEvent(new Event('offline'));
                }
              });
            }, 5000);
          `,
        }}
      />
    </>
  );
}
