<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مكتب المحاماة - صفحة طوارئ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e40af;
            text-align: center;
            margin-bottom: 20px;
        }
        .status {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background: #d1fae5;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            border: 1px solid #ef4444;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #1e40af;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .btn:hover {
            background: #1e3a8a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏛️ مكتب المحاماة للاستشارات القانونية</h1>
        
        <div class="status error">
            <h3>⚠️ حالة الطوارئ</h3>
            <p>يبدو أن هناك مشكلة في تشغيل خادم Next.js. هذه صفحة طوارئ للتأكد من أن الموقع يمكن الوصول إليه.</p>
        </div>

        <div class="status">
            <h3>📋 معلومات التشخيص</h3>
            <ul>
                <li><strong>الوقت:</strong> <span id="time"></span></li>
                <li><strong>المتصفح:</strong> <span id="browser"></span></li>
                <li><strong>الحالة:</strong> صفحة HTML ثابتة تعمل</li>
            </ul>
        </div>

        <div class="status success">
            <h3>✅ ما يعمل</h3>
            <ul>
                <li>الخادم الأساسي يعمل</li>
                <li>الملفات الثابتة متاحة</li>
                <li>HTML و CSS يعملان</li>
                <li>JavaScript يعمل (انظر الوقت أعلاه)</li>
            </ul>
        </div>

        <h3>🔧 خطوات الإصلاح المقترحة</h3>
        <ol>
            <li>إعادة تشغيل خادم Next.js</li>
            <li>التحقق من سجلات الأخطاء</li>
            <li>فحص ملفات الإعداد</li>
            <li>إعادة تثبيت التبعيات إذا لزم الأمر</li>
        </ol>

        <h3>📞 معلومات الاتصال</h3>
        <p>في حالة الطوارئ القانونية، يمكنكم التواصل معنا:</p>
        <ul>
            <li><strong>الهاتف:</strong> +966 11 123 4567</li>
            <li><strong>البريد الإلكتروني:</strong> <EMAIL></li>
            <li><strong>العنوان:</strong> الرياض، المملكة العربية السعودية</li>
        </ul>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/" class="btn">العودة للصفحة الرئيسية</a>
            <a href="/test" class="btn">صفحة الاختبار</a>
            <button onclick="location.reload()" class="btn">تحديث الصفحة</button>
        </div>

        <div style="text-align: center; margin-top: 20px; color: #666; font-size: 14px;">
            © 2024 مكتب المحاماة للاستشارات القانونية
        </div>
    </div>

    <script>
        // عرض الوقت الحالي
        function updateTime() {
            const now = new Date();
            document.getElementById('time').textContent = now.toLocaleString('ar-SA');
        }
        
        // عرض معلومات المتصفح
        document.getElementById('browser').textContent = navigator.userAgent.split(' ')[0];
        
        // تحديث الوقت كل ثانية
        updateTime();
        setInterval(updateTime, 1000);

        // محاولة الاتصال بـ API
        fetch('/api/health')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.querySelector('.status.error');
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '<h3>✅ الخادم يعمل</h3><p>تم الاتصال بـ API بنجاح!</p>';
            })
            .catch(error => {
                console.log('API غير متاح:', error);
            });
    </script>
</body>
</html>
