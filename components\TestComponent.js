import React, { useState, useEffect } from 'react';

function TestComponent() {
  const [count, setCount] = useState(0);
  const [timestamp, setTimestamp] = useState('');
  const [refreshStatus, setRefreshStatus] = useState('جاري التحميل...');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    setRefreshStatus('Fast Refresh يعمل بشكل صحيح! ✅');
    setTimestamp(new Date().toLocaleTimeString('ar-SA'));
  }, []);

  const handleIncrement = () => {
    setCount(prev => prev + 1);
    if (mounted) {
      setTimestamp(new Date().toLocaleTimeString('ar-SA'));
    }
  };

  const handleReset = () => {
    setCount(0);
    if (mounted) {
      setTimestamp(new Date().toLocaleTimeString('ar-SA'));
    }
  };

  return (
    <div className="p-6 border-2 border-blue-300 rounded-lg bg-blue-50 shadow-lg">
      <h2 className="text-2xl font-bold mb-4 text-blue-800">🧪 مكون اختبار Fast Refresh</h2>

      <div className="mb-4 p-3 bg-white rounded border">
        <p className="text-lg mb-2"><strong>العداد:</strong> {count}</p>
        <p className="text-sm text-gray-600"><strong>آخر تحديث:</strong> {mounted ? timestamp : 'جاري التحميل...'}</p>
        <p className="text-sm mt-2 font-semibold text-green-600">{refreshStatus}</p>
      </div>

      <div className="space-x-2 space-x-reverse">
        <button
          onClick={handleIncrement}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
        >
          زيادة العداد (+1)
        </button>

        <button
          onClick={handleReset}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
        >
          إعادة تعيين
        </button>
      </div>

      <div className="mt-4 p-3 bg-yellow-100 rounded border-l-4 border-yellow-500">
        <p className="text-sm text-yellow-800">
          <strong>تعليمات الاختبار:</strong> قم بتعديل هذا المكون وحفظ الملف. يجب أن ترى التغييرات فوراً دون إعادة تحميل الصفحة.
        </p>
      </div>
    </div>
  );
}

export default TestComponent;
