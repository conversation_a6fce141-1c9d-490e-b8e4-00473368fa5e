import React, { useState, useRef } from 'react';
import { 
  FaBold, 
  FaItalic, 
  FaUnderline, 
  FaListUl, 
  FaListOl, 
  FaLink,
  FaImage,
  FaCode,
  FaQuoteLeft,
  FaUndo,
  FaRedo
} from 'react-icons/fa';

const SimpleRichTextEditor = ({ 
  value = '', 
  onChange, 
  placeholder = 'اكتب المحتوى هنا...',
  className = '',
  minHeight = '200px'
}) => {
  const editorRef = useRef(null);
  const [isEditorFocused, setIsEditorFocused] = useState(false);

  const executeCommand = (command, value = null) => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    handleContentChange();
  };

  const handleContentChange = () => {
    if (editorRef.current && onChange) {
      const content = editorRef.current.innerHTML;
      onChange(content);
    }
  };

  const insertLink = () => {
    const url = prompt('أدخل رابط URL:');
    if (url) {
      executeCommand('createLink', url);
    }
  };

  const insertImage = () => {
    const url = prompt('أدخل رابط الصورة:');
    if (url) {
      executeCommand('insertImage', url);
    }
  };

  const toolbarButtons = [
    { command: 'bold', icon: FaBold, title: 'عريض' },
    { command: 'italic', icon: FaItalic, title: 'مائل' },
    { command: 'underline', icon: FaUnderline, title: 'تحته خط' },
    { command: 'separator' },
    { command: 'insertUnorderedList', icon: FaListUl, title: 'قائمة نقطية' },
    { command: 'insertOrderedList', icon: FaListOl, title: 'قائمة مرقمة' },
    { command: 'separator' },
    { command: 'link', icon: FaLink, title: 'إدراج رابط', action: insertLink },
    { command: 'image', icon: FaImage, title: 'إدراج صورة', action: insertImage },
    { command: 'separator' },
    { command: 'formatBlock', icon: FaQuoteLeft, title: 'اقتباس', value: 'blockquote' },
    { command: 'formatBlock', icon: FaCode, title: 'كود', value: 'pre' },
    { command: 'separator' },
    { command: 'undo', icon: FaUndo, title: 'تراجع' },
    { command: 'redo', icon: FaRedo, title: 'إعادة' }
  ];

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 p-2 flex flex-wrap gap-1">
        {toolbarButtons.map((button, index) => {
          if (button.command === 'separator') {
            return <div key={index} className="w-px bg-gray-300 mx-1" />;
          }

          const Icon = button.icon;
          return (
            <button
              key={button.command}
              type="button"
              onClick={() => {
                if (button.action) {
                  button.action();
                } else {
                  executeCommand(button.command, button.value);
                }
              }}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded transition-colors"
              title={button.title}
            >
              <Icon size={14} />
            </button>
          );
        })}
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable
        className={`p-4 outline-none ${isEditorFocused ? 'ring-2 ring-blue-500' : ''}`}
        style={{ minHeight }}
        onInput={handleContentChange}
        onFocus={() => setIsEditorFocused(true)}
        onBlur={() => setIsEditorFocused(false)}
        dangerouslySetInnerHTML={{ __html: value }}
        data-placeholder={placeholder}
      />

      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
        }
        
        [contenteditable] blockquote {
          border-left: 4px solid #e5e7eb;
          padding-left: 1rem;
          margin: 1rem 0;
          font-style: italic;
          color: #6b7280;
        }
        
        [contenteditable] pre {
          background-color: #f3f4f6;
          padding: 1rem;
          border-radius: 0.375rem;
          overflow-x: auto;
          font-family: 'Courier New', monospace;
        }
        
        [contenteditable] ul, [contenteditable] ol {
          padding-left: 2rem;
          margin: 1rem 0;
        }
        
        [contenteditable] li {
          margin: 0.5rem 0;
        }
        
        [contenteditable] a {
          color: #3b82f6;
          text-decoration: underline;
        }
        
        [contenteditable] img {
          max-width: 100%;
          height: auto;
          border-radius: 0.375rem;
          margin: 1rem 0;
        }
      `}</style>
    </div>
  );
};

export default SimpleRichTextEditor;
