@echo off
echo ========================================
echo Quick Diagnosis for Next.js Project
echo ========================================
echo.

echo Checking Node.js version...
node --version
echo.

echo Checking npm version...
npm --version
echo.

echo Checking Next.js installation...
npm list next
echo.

echo Checking if port 3000 is in use...
netstat -ano | findstr :3000
echo.

echo Checking project files...
if exist package.json (
    echo ✓ package.json exists
) else (
    echo ✗ package.json missing
)

if exist next.config.js (
    echo ✓ next.config.js exists
) else (
    echo ✗ next.config.js missing
)

if exist pages\index.js (
    echo ✓ pages/index.js exists
) else (
    echo ✗ pages/index.js missing
)

echo.
echo Attempting to start server on port 3001...
npm run dev -- -p 3001

pause
