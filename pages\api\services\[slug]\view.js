import connectDB from '../../../../lib/mongodb';
import Service from '../../../../lib/models/Service';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  const { slug } = req.query;

  if (!slug) {
    return res.status(400).json({
      success: false,
      message: 'معرف الخدمة مطلوب'
    });
  }

  try {
    await connectDB();

    const service = await Service.findOne({ slug, status: 'published' });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'الخدمة غير موجودة'
      });
    }

    // زيادة عدد المشاهدات
    await service.incrementViews();

    return res.status(200).json({
      success: true,
      views: service.views + 1
    });
  } catch (error) {
    console.error('Error incrementing views:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في تحديث المشاهدات'
    });
  }
}
