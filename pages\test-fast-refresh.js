import React from 'react';
import Layout from '../components/Layout';
import TestComponent from '../components/TestComponent';

export default function TestFastRefresh() {
  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
            🚀 صفحة اختبار Fast Refresh
          </h1>
          
          <div className="mb-8 p-4 bg-green-100 rounded-lg border-l-4 border-green-500">
            <h2 className="text-xl font-semibold text-green-800 mb-2">كيفية الاختبار:</h2>
            <ol className="list-decimal list-inside text-green-700 space-y-1">
              <li>افتح هذه الصفحة في المتصفح</li>
              <li>افتح ملف components/TestComponent.js في المحرر</li>
              <li>قم بتعديل أي نص في المكون</li>
              <li>احفظ الملف (Ctrl+S)</li>
              <li>يجب أن ترى التغييرات فوراً دون إعادة تحميل الصفحة</li>
            </ol>
          </div>

          <TestComponent />
          
          <div className="mt-8 p-4 bg-blue-100 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">معلومات النظام:</h3>
            <ul className="text-blue-700 space-y-1">
              <li><strong>Next.js:</strong> {process.env.NODE_ENV === 'development' ? 'وضع التطوير' : 'وضع الإنتاج'}</li>
              <li><strong>Fast Refresh:</strong> {process.env.FAST_REFRESH === 'true' ? 'مفعل ✅' : 'غير مفعل ❌'}</li>
              <li><strong>الوقت الحالي:</strong> {new Date().toLocaleString('ar-SA')}</li>
            </ul>
          </div>
        </div>
      </div>
    </Layout>
  );
}
